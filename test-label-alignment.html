<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>标签对齐测试</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: #000;
            font-family: Arial, sans-serif;
            overflow: hidden;
        }
        
        #container {
            width: 100vw;
            height: 100vh;
        }
        
        #info {
            position: absolute;
            top: 10px;
            left: 10px;
            color: white;
            background: rgba(0, 0, 0, 0.7);
            padding: 10px;
            border-radius: 5px;
            z-index: 1000;
        }
        
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            margin: 5px;
            border-radius: 4px;
            cursor: pointer;
        }
        
        .test-button:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <div id="info">
        <h3>标签对齐测试</h3>
        <p>测试文字标签与marker的垂直对齐</p>
        <button class="test-button" onclick="createTestLabels()">创建测试标签</button>
        <button class="test-button" onclick="clearTestLabels()">清除标签</button>
        <button class="test-button" onclick="toggleWireframe()">切换线框模式</button>
    </div>
    
    <div id="container"></div>

    <script type="module">
        import * as THREE from 'https://unpkg.com/three@0.158.0/build/three.module.js';
        import { OrbitControls } from 'https://unpkg.com/three@0.158.0/examples/jsm/controls/OrbitControls.js';
        
        // 全局变量
        let scene, camera, renderer, controls;
        let testLabels = [];
        let testMarkers = [];
        let earth;
        
        // 初始化场景
        function init() {
            // 创建场景
            scene = new THREE.Scene();
            
            // 创建相机
            camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
            camera.position.set(0, 0, 15);
            
            // 创建渲染器
            renderer = new THREE.WebGLRenderer({ antialias: true });
            renderer.setSize(window.innerWidth, window.innerHeight);
            renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2));
            document.getElementById('container').appendChild(renderer.domElement);
            
            // 创建控制器
            controls = new OrbitControls(camera, renderer.domElement);
            controls.enableDamping = true;
            controls.dampingFactor = 0.05;
            
            // 创建简单的地球
            createEarth();
            
            // 添加光源
            const ambientLight = new THREE.AmbientLight(0x404040, 0.6);
            scene.add(ambientLight);
            
            const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
            directionalLight.position.set(1, 1, 1);
            scene.add(directionalLight);
            
            // 开始渲染循环
            animate();
            
            // 窗口大小调整
            window.addEventListener('resize', onWindowResize);
        }
        
        // 创建简单的地球
        function createEarth() {
            const geometry = new THREE.SphereGeometry(5, 32, 32);
            const material = new THREE.MeshPhongMaterial({ 
                color: 0x4444ff,
                wireframe: false
            });
            earth = new THREE.Mesh(geometry, material);
            scene.add(earth);
        }
        
        // 创建测试标签和marker
        function createTestLabels() {
            clearTestLabels();
            
            // 测试位置数组
            const testPositions = [
                { pos: new THREE.Vector3(6, 0, 0), text: "东方", color: 0xff0000 },
                { pos: new THREE.Vector3(-6, 0, 0), text: "西方", color: 0x00ff00 },
                { pos: new THREE.Vector3(0, 6, 0), text: "上方", color: 0x0000ff },
                { pos: new THREE.Vector3(0, -6, 0), text: "下方", color: 0xffff00 },
                { pos: new THREE.Vector3(4, 4, 0), text: "东北", color: 0xff00ff },
                { pos: new THREE.Vector3(-4, -4, 0), text: "西南", color: 0x00ffff }
            ];
            
            testPositions.forEach((item, index) => {
                // 创建marker
                const markerGeometry = new THREE.SphereGeometry(0.2, 16, 16);
                const markerMaterial = new THREE.MeshBasicMaterial({ color: item.color });
                const marker = new THREE.Mesh(markerGeometry, markerMaterial);
                marker.position.copy(item.pos);
                scene.add(marker);
                testMarkers.push(marker);
                
                // 创建标签
                const label = createTextLabel(item.pos, item.text, {
                    fontSize: 32,
                    color: "#ffffff",
                    backgroundColor: "rgba(0, 0, 0, 0.8)",
                    borderColor: "#ffffff",
                    borderWidth: 2,
                    padding: 10,
                    borderRadius: 8,
                    scale: 2.0
                });
                
                // 计算标签偏移位置（向右偏移1.5单位）
                const offsetDirection = new THREE.Vector3(1, 0, 0);
                const labelPosition = item.pos.clone().add(offsetDirection.multiplyScalar(1.5));
                label.position.copy(labelPosition);
                
                scene.add(label);
                testLabels.push(label);
                
                // 创建连接线来显示对齐关系
                const lineGeometry = new THREE.BufferGeometry().setFromPoints([
                    item.pos,
                    labelPosition
                ]);
                const lineMaterial = new THREE.LineBasicMaterial({ 
                    color: 0xffffff, 
                    opacity: 0.5, 
                    transparent: true 
                });
                const line = new THREE.Line(lineGeometry, lineMaterial);
                scene.add(line);
                testLabels.push(line);
            });
        }
        
        // 清除测试标签
        function clearTestLabels() {
            testLabels.forEach(label => {
                scene.remove(label);
                if (label.geometry) label.geometry.dispose();
                if (label.material) {
                    if (label.material.map) label.material.map.dispose();
                    label.material.dispose();
                }
            });
            testLabels = [];
            
            testMarkers.forEach(marker => {
                scene.remove(marker);
                if (marker.geometry) marker.geometry.dispose();
                if (marker.material) marker.material.dispose();
            });
            testMarkers = [];
        }
        
        // 切换线框模式
        function toggleWireframe() {
            if (earth) {
                earth.material.wireframe = !earth.material.wireframe;
            }
        }
        
        // 创建文字标签（简化版本）
        function createTextLabel(position, text, options = {}) {
            const canvas = document.createElement('canvas');
            const context = canvas.getContext('2d');
            
            // 设置默认选项
            const config = {
                fontSize: 32,
                fontFamily: "Arial, sans-serif",
                color: "#ffffff",
                backgroundColor: "rgba(0, 0, 0, 0.7)",
                borderColor: "#ffffff",
                borderWidth: 2,
                padding: 10,
                borderRadius: 8,
                scale: 1.0,
                ...options
            };
            
            // 设置字体
            context.font = `${config.fontSize}px ${config.fontFamily}`;
            
            // 测量文字尺寸
            const metrics = context.measureText(text);
            const textWidth = metrics.width;
            const textHeight = config.fontSize;
            
            // 计算Canvas尺寸
            const canvasWidth = textWidth + config.padding * 2;
            const canvasHeight = textHeight + config.padding * 2;
            
            // 设置Canvas尺寸
            canvas.width = canvasWidth;
            canvas.height = canvasHeight;
            
            // 重新设置字体（Canvas尺寸改变后需要重新设置）
            context.font = `${config.fontSize}px ${config.fontFamily}`;
            context.textAlign = "center";
            context.textBaseline = "middle";
            
            // 绘制背景
            if (config.backgroundColor && config.backgroundColor !== "transparent") {
                context.fillStyle = config.backgroundColor;
                if (config.borderRadius > 0) {
                    drawRoundedRect(context, 0, 0, canvasWidth, canvasHeight, config.borderRadius);
                } else {
                    context.fillRect(0, 0, canvasWidth, canvasHeight);
                }
            }
            
            // 绘制边框
            if (config.borderWidth > 0 && config.borderColor) {
                context.strokeStyle = config.borderColor;
                context.lineWidth = config.borderWidth;
                if (config.borderRadius > 0) {
                    drawRoundedRect(context, 0, 0, canvasWidth, canvasHeight, config.borderRadius, false);
                } else {
                    context.strokeRect(0, 0, canvasWidth, canvasHeight);
                }
            }
            
            // 绘制文字（在Canvas中心）
            context.fillStyle = config.color;
            context.fillText(text, canvasWidth / 2, canvasHeight / 2);
            
            // 创建纹理
            const texture = new THREE.CanvasTexture(canvas);
            texture.needsUpdate = true;
            
            // 计算标签尺寸
            const aspect = canvas.width / canvas.height;
            const labelWidth = config.scale;
            const labelHeight = labelWidth / aspect;
            
            // 创建平面几何体
            const geometry = new THREE.PlaneGeometry(labelWidth, labelHeight);
            
            // 创建材质
            const material = new THREE.MeshBasicMaterial({
                map: texture,
                transparent: true,
                side: THREE.DoubleSide,
                alphaTest: 0.1
            });
            
            // 创建网格
            const mesh = new THREE.Mesh(geometry, material);
            
            // 让标签始终面向相机
            mesh.lookAt(camera.position);
            
            return mesh;
        }
        
        // 绘制圆角矩形
        function drawRoundedRect(context, x, y, width, height, radius, fill = true) {
            context.beginPath();
            context.moveTo(x + radius, y);
            context.lineTo(x + width - radius, y);
            context.quadraticCurveTo(x + width, y, x + width, y + radius);
            context.lineTo(x + width, y + height - radius);
            context.quadraticCurveTo(x + width, y + height, x + width - radius, y + height);
            context.lineTo(x + radius, y + height);
            context.quadraticCurveTo(x, y + height, x, y + height - radius);
            context.lineTo(x, y + radius);
            context.quadraticCurveTo(x, y, x + radius, y);
            context.closePath();
            
            if (fill) {
                context.fill();
            } else {
                context.stroke();
            }
        }
        
        // 渲染循环
        function animate() {
            requestAnimationFrame(animate);
            
            controls.update();
            
            // 让所有标签面向相机
            testLabels.forEach(label => {
                if (label.isLine) return; // 跳过连接线
                if (label.lookAt) {
                    label.lookAt(camera.position);
                }
            });
            
            renderer.render(scene, camera);
        }
        
        // 窗口大小调整
        function onWindowResize() {
            camera.aspect = window.innerWidth / window.innerHeight;
            camera.updateProjectionMatrix();
            renderer.setSize(window.innerWidth, window.innerHeight);
        }
        
        // 将函数暴露到全局作用域
        window.createTestLabels = createTestLabels;
        window.clearTestLabels = clearTestLabels;
        window.toggleWireframe = toggleWireframe;
        
        // 初始化
        init();
    </script>
</body>
</html>
