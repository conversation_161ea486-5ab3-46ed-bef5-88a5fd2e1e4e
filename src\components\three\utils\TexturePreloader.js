import * as THREE from "three";
import { PATHS, TEXTURE_PRELOAD_CONFIG } from "../../../constants/index.js";

/**
 * 纹理预加载管理器
 * 负责预加载所有地球相关纹理，确保动画开始前所有纹理都已加载完成
 */
class TexturePreloader {
  constructor() {
    this.textures = new Map();
    this.loadingPromises = new Map();
    this.isLoading = false;
    this.loadProgress = 0;
    this.totalTextures = 0;
    this.loadedTextures = 0;
    
    // 事件监听器
    this.eventListeners = new Map();
    
    // 纹理加载器
    this.textureLoader = new THREE.TextureLoader();
    
    // 配置加载管理器来跟踪进度
    this.loadingManager = new THREE.LoadingManager();
    this.setupLoadingManager();
  }

  /**
   * 设置加载管理器
   */
  setupLoadingManager() {
    this.loadingManager.onStart = (url, itemsLoaded, itemsTotal) => {
      console.log(`开始加载纹理: ${url} (${itemsLoaded}/${itemsTotal})`);
      this.dispatchEvent('loadStart', { url, itemsLoaded, itemsTotal });
    };

    this.loadingManager.onProgress = (url, itemsLoaded, itemsTotal) => {
      this.loadedTextures = itemsLoaded;
      this.totalTextures = itemsTotal;
      this.loadProgress = itemsTotal > 0 ? (itemsLoaded / itemsTotal) * 100 : 0;
      
      console.log(`纹理加载进度: ${this.loadProgress.toFixed(1)}% (${itemsLoaded}/${itemsTotal})`);
      this.dispatchEvent('loadProgress', { 
        url, 
        itemsLoaded, 
        itemsTotal, 
        progress: this.loadProgress 
      });
    };

    this.loadingManager.onLoad = () => {
      console.log('所有纹理加载完成');
      this.isLoading = false;
      this.dispatchEvent('loadComplete', { 
        totalTextures: this.totalTextures,
        loadedTextures: this.loadedTextures 
      });
    };

    this.loadingManager.onError = (url) => {
      console.error(`纹理加载失败: ${url}`);
      this.dispatchEvent('loadError', { url });
    };

    // 使用加载管理器创建纹理加载器
    this.textureLoader = new THREE.TextureLoader(this.loadingManager);
  }

  /**
   * 添加事件监听器
   */
  addEventListener(event, callback) {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, []);
    }
    this.eventListeners.get(event).push(callback);
  }

  /**
   * 移除事件监听器
   */
  removeEventListener(event, callback) {
    if (this.eventListeners.has(event)) {
      const listeners = this.eventListeners.get(event);
      const index = listeners.indexOf(callback);
      if (index > -1) {
        listeners.splice(index, 1);
      }
    }
  }

  /**
   * 触发事件
   */
  dispatchEvent(event, data) {
    if (this.eventListeners.has(event)) {
      this.eventListeners.get(event).forEach(callback => {
        try {
          callback(data);
        } catch (error) {
          console.error(`Error in event listener for ${event}:`, error);
        }
      });
    }
  }

  /**
   * 预加载单个纹理
   */
  loadTexture(url, key = null) {
    const textureKey = key || url;
    
    // 如果纹理已经加载过，直接返回
    if (this.textures.has(textureKey)) {
      return Promise.resolve(this.textures.get(textureKey));
    }

    // 如果正在加载，返回现有的Promise
    if (this.loadingPromises.has(textureKey)) {
      return this.loadingPromises.get(textureKey);
    }

    // 创建加载Promise
    const loadPromise = new Promise((resolve, reject) => {
      const timeoutId = setTimeout(() => {
        reject(new Error(`纹理加载超时: ${url}`));
      }, TEXTURE_PRELOAD_CONFIG.timeout);

      this.textureLoader.load(
        url,
        (texture) => {
          clearTimeout(timeoutId);
          this.textures.set(textureKey, texture);
          this.loadingPromises.delete(textureKey);
          resolve(texture);
        },
        undefined,
        (error) => {
          clearTimeout(timeoutId);
          this.loadingPromises.delete(textureKey);
          reject(error);
        }
      );
    });

    this.loadingPromises.set(textureKey, loadPromise);
    return loadPromise;
  }

  /**
   * 预加载所有地球纹理
   */
  async preloadAllTextures() {
    if (!TEXTURE_PRELOAD_CONFIG.enabled) {
      console.log('纹理预加载已禁用');
      return {};
    }

    if (this.isLoading) {
      console.log('纹理预加载已在进行中...');
      return this.waitForCompletion();
    }

    console.log('开始预加载所有地球纹理...');
    this.isLoading = true;
    this.loadProgress = 0;
    this.loadedTextures = 0;
    this.totalTextures = Object.keys(PATHS).length;

    try {
      // 并行加载所有纹理
      const loadPromises = Object.entries(PATHS).map(([key, path]) => 
        this.loadTexture(path, key)
      );

      const textures = await Promise.all(loadPromises);
      
      // 创建纹理映射对象
      const textureMap = {};
      Object.keys(PATHS).forEach((key, index) => {
        textureMap[key] = textures[index];
      });

      console.log('所有纹理预加载完成:', Object.keys(textureMap));
      return textureMap;

    } catch (error) {
      console.error('纹理预加载失败:', error);
      this.isLoading = false;
      throw error;
    }
  }

  /**
   * 等待当前加载完成
   */
  waitForCompletion() {
    return new Promise((resolve, reject) => {
      if (!this.isLoading) {
        // 如果没有在加载，返回已加载的纹理
        const textureMap = {};
        Object.keys(PATHS).forEach(key => {
          if (this.textures.has(key)) {
            textureMap[key] = this.textures.get(key);
          }
        });
        resolve(textureMap);
        return;
      }

      // 监听加载完成事件
      const onComplete = () => {
        this.removeEventListener('loadComplete', onComplete);
        this.removeEventListener('loadError', onError);
        
        const textureMap = {};
        Object.keys(PATHS).forEach(key => {
          if (this.textures.has(key)) {
            textureMap[key] = this.textures.get(key);
          }
        });
        resolve(textureMap);
      };

      const onError = (data) => {
        this.removeEventListener('loadComplete', onComplete);
        this.removeEventListener('loadError', onError);
        reject(new Error(`纹理加载失败: ${data.url}`));
      };

      this.addEventListener('loadComplete', onComplete);
      this.addEventListener('loadError', onError);
    });
  }

  /**
   * 获取已加载的纹理
   */
  getTexture(key) {
    return this.textures.get(key);
  }

  /**
   * 获取所有已加载的纹理
   */
  getAllTextures() {
    const textureMap = {};
    Object.keys(PATHS).forEach(key => {
      if (this.textures.has(key)) {
        textureMap[key] = this.textures.get(key);
      }
    });
    return textureMap;
  }

  /**
   * 获取加载进度
   */
  getProgress() {
    return {
      progress: this.loadProgress,
      loaded: this.loadedTextures,
      total: this.totalTextures,
      isLoading: this.isLoading
    };
  }

  /**
   * 清理资源
   */
  dispose() {
    // 清理纹理
    this.textures.forEach(texture => {
      if (texture && texture.dispose) {
        texture.dispose();
      }
    });
    
    this.textures.clear();
    this.loadingPromises.clear();
    this.eventListeners.clear();
    this.isLoading = false;
  }
}

// 创建单例实例
const texturePreloader = new TexturePreloader();

export { TexturePreloader, texturePreloader };
